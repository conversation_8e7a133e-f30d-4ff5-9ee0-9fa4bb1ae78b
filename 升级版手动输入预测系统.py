#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
升级版手动输入预测系统 - 集成数据管理
1. 用户输入真实数据自动添加到主数据文件
2. 预测数据自动保存到专门的CSV文件
3. 完整的数据管理和统计功能
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class UpgradedManualPredictionSystem:
    """升级版手动输入预测系统"""
    
    def __init__(self):
        self.main_data_file = "data/processed/lottery_data_clean_no_special.csv"
        self.prediction_data_file = "prediction_data.csv"
        self.prediction_history_file = "prediction_history.json"
        
        # 34.3%方法的最佳参数
        self.optimal_params = {
            'high_freq_boost': 1.15,
            'low_freq_penalty': 0.85,
            'rising_trend_boost': 1.10,
            'falling_trend_penalty': 0.90,
            'perturbation': 0.05
        }
        
        # 数字分类
        self.high_freq_numbers = [5, 15, 3, 40, 30]
        self.low_freq_numbers = [41, 1, 8, 48, 47]
        self.rising_numbers = [30, 39, 4, 8, 22]
        self.falling_numbers = [5, 26, 44, 36, 15]
        
        # 模型组件
        self.enhanced_markov_prob = None
        
    def load_data_and_build_model(self):
        """加载数据并构建模型"""
        print("🔧 加载数据并构建34.3%预测模型...")
        
        try:
            # 加载主数据
            self.main_data = pd.read_csv(self.main_data_file)
            self.main_data = self.main_data.sort_values(['年份', '期号']).reset_index(drop=True)
            
            print(f"✅ 主数据加载完成: {len(self.main_data)}期")
            
            # 构建增强马尔可夫模型
            self.build_enhanced_markov_model()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_enhanced_markov_model(self):
        """构建增强马尔可夫模型"""
        # 使用2023-2024年作为训练数据
        train_data = self.main_data[
            (self.main_data['年份'] >= 2023) & 
            (self.main_data['年份'] <= 2024)
        ].copy()
        
        # 基础马尔可夫转移概率
        transition_count = defaultdict(lambda: defaultdict(int))
        
        for i in range(len(train_data) - 1):
            current_numbers = set([train_data.iloc[i][f'数字{j}'] for j in range(1, 7)])
            next_numbers = set([train_data.iloc[i+1][f'数字{j}'] for j in range(1, 7)])
            
            for curr_num in current_numbers:
                for next_num in next_numbers:
                    transition_count[curr_num][next_num] += 1
        
        # 计算基础转移概率
        base_markov_prob = {}
        for curr_num in range(1, 50):
            if curr_num in transition_count:
                total = sum(transition_count[curr_num].values())
                smoothed_total = total + 49
                base_markov_prob[curr_num] = {}
                
                for next_num in range(1, 50):
                    count = transition_count[curr_num].get(next_num, 0)
                    base_markov_prob[curr_num][next_num] = (count + 1) / smoothed_total
            else:
                base_markov_prob[curr_num] = {
                    next_num: 1/49 for next_num in range(1, 50)
                }
        
        # 应用增强策略
        self.apply_enhancements(base_markov_prob)
        
        print("✅ 34.3%增强马尔可夫模型构建完成")
    
    def apply_enhancements(self, base_markov_prob):
        """应用增强策略"""
        # 计算权重
        frequency_weights = {}
        trend_weights = {}
        
        for num in range(1, 50):
            if num in self.high_freq_numbers:
                frequency_weights[num] = self.optimal_params['high_freq_boost']
            elif num in self.low_freq_numbers:
                frequency_weights[num] = self.optimal_params['low_freq_penalty']
            else:
                frequency_weights[num] = 1.0
            
            if num in self.rising_numbers:
                trend_weights[num] = self.optimal_params['rising_trend_boost']
            elif num in self.falling_numbers:
                trend_weights[num] = self.optimal_params['falling_trend_penalty']
            else:
                trend_weights[num] = 1.0
        
        # 构建增强马尔可夫概率
        self.enhanced_markov_prob = {}
        for curr_num in base_markov_prob:
            self.enhanced_markov_prob[curr_num] = {}
            total_weight = 0
            
            for next_num, base_prob in base_markov_prob[curr_num].items():
                freq_weight = frequency_weights[next_num]
                trend_weight = trend_weights[next_num]
                combined_weight = freq_weight * trend_weight
                weighted_prob = base_prob * combined_weight
                
                self.enhanced_markov_prob[curr_num][next_num] = weighted_prob
                total_weight += weighted_prob
            
            # 归一化
            for next_num in self.enhanced_markov_prob[curr_num]:
                self.enhanced_markov_prob[curr_num][next_num] /= total_weight
    
    def predict_next_period(self, current_numbers):
        """预测下期数字"""
        if self.enhanced_markov_prob is None:
            raise RuntimeError("模型未初始化")
        
        # 使用固定随机种子确保可重复性
        np.random.seed(42)
        
        number_probs = defaultdict(float)
        total_prob = 0.0
        
        current_set = set(current_numbers)
        
        for curr_num in current_set:
            if curr_num in self.enhanced_markov_prob:
                for next_num, prob in self.enhanced_markov_prob[curr_num].items():
                    number_probs[next_num] += prob
                    total_prob += prob
        
        if total_prob > 0:
            for num in number_probs:
                number_probs[num] /= total_prob
        
        # 添加随机扰动
        perturbation = self.optimal_params['perturbation']
        for num in number_probs:
            noise = np.random.normal(0, perturbation * number_probs[num])
            number_probs[num] = max(0, number_probs[num] + noise)
        
        # 选择前2个数字
        if len(number_probs) >= 2:
            sorted_numbers = sorted(number_probs.items(), key=lambda x: x[1], reverse=True)
            prediction = [num for num, prob in sorted_numbers[:2]]
            confidence = (sorted_numbers[0][1] + sorted_numbers[1][1]) / 2
        else:
            prediction = [1, 2]
            confidence = 0.02
        
        return prediction, confidence
    
    def add_real_data_to_main_file(self, year, period, numbers):
        """将真实数据添加到主数据文件"""
        try:
            # 检查数据是否已存在
            existing = self.main_data[
                (self.main_data['年份'] == year) & 
                (self.main_data['期号'] == period)
            ]
            
            if len(existing) > 0:
                print(f"⚠️ {year}年{period}期数据已存在，更新数据")
                # 更新现有数据
                idx = existing.index[0]
                for i, num in enumerate(sorted(numbers), 1):
                    self.main_data.loc[idx, f'数字{i}'] = num
            else:
                # 添加新数据
                new_row = {
                    '年份': year,
                    '期号': period
                }
                for i, num in enumerate(sorted(numbers), 1):
                    new_row[f'数字{i}'] = num
                
                # 使用pd.concat替代append
                new_df = pd.DataFrame([new_row])
                self.main_data = pd.concat([self.main_data, new_df], ignore_index=True)
                self.main_data = self.main_data.sort_values(['年份', '期号']).reset_index(drop=True)
                print(f"✅ 添加 {year}年{period}期数据到主文件")
            
            # 保存到文件
            self.main_data.to_csv(self.main_data_file, index=False, encoding='utf-8')
            
            return True
            
        except Exception as e:
            print(f"❌ 添加数据到主文件失败: {e}")
            return False
    
    def verify_previous_prediction(self, current_year, current_period, current_numbers):
        """验证上期预测"""
        try:
            if not os.path.exists(self.prediction_data_file):
                return False

            # 读取预测数据
            prediction_df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')

            # 查找对应当期的预测记录
            target_period_name = f"{current_year}年{current_period}期"

            matching_predictions = prediction_df[
                prediction_df['预测期号'] == target_period_name
            ]

            if len(matching_predictions) == 0:
                print(f"📝 没有找到{target_period_name}的预测记录")
                return False

            # 使用最新的预测记录
            prediction_row = matching_predictions.iloc[-1]
            row_index = matching_predictions.index[-1]

            # 检查是否已经验证过
            if not pd.isna(prediction_row['实际数字1']) and prediction_row['实际数字1'] != '':
                print(f"📝 {target_period_name}的预测已经验证过")
                return False

            # 获取预测数字
            predicted_numbers = [prediction_row['预测数字1'], prediction_row['预测数字2']]

            # 计算命中情况
            predicted_set = set(predicted_numbers)
            actual_set = set(current_numbers)
            hit_numbers = predicted_set & actual_set
            hit_count = len(hit_numbers)
            is_hit = hit_count >= 1

            # 更新预测记录
            for i, num in enumerate(current_numbers, 1):
                prediction_df.loc[row_index, f'实际数字{i}'] = num

            prediction_df.loc[row_index, '命中数量'] = hit_count
            prediction_df.loc[row_index, '是否命中'] = '是' if is_hit else '否'
            prediction_df.loc[row_index, '命中数字'] = ','.join(map(str, sorted(hit_numbers))) if hit_numbers else ''

            # 更新备注
            current_note = prediction_df.loc[row_index, '备注']
            if pd.isna(current_note) or current_note == '':
                prediction_df.loc[row_index, '备注'] = '用户验证'
            else:
                prediction_df.loc[row_index, '备注'] = f"{current_note},用户验证"

            # 保存更新后的文件
            prediction_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')

            # 显示验证结果
            print(f"\n🎯 自动验证上期预测:")
            print(f"预测期号: {target_period_name}")
            print(f"预测数字: {predicted_numbers}")
            print(f"实际开奖: {current_numbers}")
            print(f"命中数字: {sorted(hit_numbers) if hit_numbers else '无'}")
            print(f"命中数量: {hit_count}")
            print(f"命中状态: {'✅ 命中' if is_hit else '❌ 未命中'}")
            print(f"✅ 预测验证结果已更新到CSV文件")

            return True

        except Exception as e:
            print(f"❌ 验证上期预测失败: {e}")
            return False

    def add_prediction_to_csv(self, prediction_data):
        """添加预测到CSV文件"""
        try:
            # 读取现有数据
            if os.path.exists(self.prediction_data_file):
                existing_df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')
            else:
                # 创建新文件
                columns = [
                    '预测日期', '预测时间', '当期年份', '当期期号', '预测期号',
                    '当期数字1', '当期数字2', '当期数字3', '当期数字4', '当期数字5', '当期数字6',
                    '预测数字1', '预测数字2', '预测置信度', '预测方法',
                    '实际数字1', '实际数字2', '实际数字3', '实际数字4', '实际数字5', '实际数字6',
                    '命中数量', '是否命中', '命中数字', '备注'
                ]
                existing_df = pd.DataFrame(columns=columns)

            # 准备新记录
            current_time = datetime.now()
            current_numbers = prediction_data.get('current_numbers', [])
            predicted_numbers = prediction_data.get('predicted_numbers', [])
            actual_numbers = prediction_data.get('actual_numbers', [])

            new_record = {
                '预测日期': current_time.strftime('%Y-%m-%d'),
                '预测时间': current_time.strftime('%H:%M:%S'),
                '当期年份': prediction_data.get('current_year', ''),
                '当期期号': prediction_data.get('current_period', ''),
                '预测期号': prediction_data.get('predicted_period', ''),
                '当期数字1': current_numbers[0] if len(current_numbers) > 0 else '',
                '当期数字2': current_numbers[1] if len(current_numbers) > 1 else '',
                '当期数字3': current_numbers[2] if len(current_numbers) > 2 else '',
                '当期数字4': current_numbers[3] if len(current_numbers) > 3 else '',
                '当期数字5': current_numbers[4] if len(current_numbers) > 4 else '',
                '当期数字6': current_numbers[5] if len(current_numbers) > 5 else '',
                '预测数字1': predicted_numbers[0] if len(predicted_numbers) > 0 else '',
                '预测数字2': predicted_numbers[1] if len(predicted_numbers) > 1 else '',
                '预测置信度': f"{prediction_data.get('confidence', 0):.6f}",
                '预测方法': prediction_data.get('method', '34.3%增强马尔可夫'),
                '实际数字1': actual_numbers[0] if len(actual_numbers) > 0 else '',
                '实际数字2': actual_numbers[1] if len(actual_numbers) > 1 else '',
                '实际数字3': actual_numbers[2] if len(actual_numbers) > 2 else '',
                '实际数字4': actual_numbers[3] if len(actual_numbers) > 3 else '',
                '实际数字5': actual_numbers[4] if len(actual_numbers) > 4 else '',
                '实际数字6': actual_numbers[5] if len(actual_numbers) > 5 else '',
                '命中数量': prediction_data.get('hit_count', ''),
                '是否命中': '是' if prediction_data.get('is_hit') else '否' if prediction_data.get('is_hit') is not None else '',
                '命中数字': ','.join(map(str, prediction_data.get('hit_numbers', []))) if prediction_data.get('hit_numbers') else '',
                '备注': prediction_data.get('notes', '用户输入')
            }

            # 添加新记录
            new_df = pd.DataFrame([new_record])
            updated_df = pd.concat([existing_df, new_df], ignore_index=True)

            # 保存到文件
            updated_df.to_csv(self.prediction_data_file, index=False, encoding='utf-8-sig')

            print(f"✅ 预测已保存到CSV文件: {self.prediction_data_file}")
            return True

        except Exception as e:
            print(f"❌ 保存预测到CSV失败: {e}")
            return False
    
    def input_current_period_data(self):
        """输入当期数据"""
        print("\n📝 请输入当期开奖数据")
        print("=" * 40)
        
        while True:
            try:
                # 输入期号信息
                year = input("请输入年份 (如: 2025): ").strip()
                period = input("请输入期号 (如: 187): ").strip()
                
                if not year.isdigit() or not period.isdigit():
                    print("❌ 年份和期号必须是数字，请重新输入")
                    continue
                
                year = int(year)
                period = int(period)
                
                # 输入开奖数字
                print("请输入6个开奖数字:")
                print("支持格式: 空格分隔(5 12 23 31 40 45) 或 逗号分隔(5,12,23,31,40,45)")
                numbers_input = input("开奖数字: ").strip()
                
                # 解析数字 - 支持多种分隔符
                if ',' in numbers_input:
                    # 逗号分隔
                    numbers_str = numbers_input.replace(' ', '').split(',')
                else:
                    # 空格分隔
                    numbers_str = numbers_input.split()
                
                # 转换为整数并处理前导零
                numbers = []
                for num_str in numbers_str:
                    num_str = num_str.strip()
                    if num_str:  # 非空字符串
                        numbers.append(int(num_str))
                
                # 验证输入
                if len(numbers) != 6:
                    print("❌ 必须输入6个数字，请重新输入")
                    continue
                
                if not all(1 <= num <= 49 for num in numbers):
                    print("❌ 数字必须在1-49范围内，请重新输入")
                    continue
                
                if len(set(numbers)) != 6:
                    print("❌ 数字不能重复，请重新输入")
                    continue
                
                return {
                    'year': year,
                    'period': period,
                    'numbers': sorted(numbers)
                }
                
            except ValueError:
                print("❌ 输入格式错误，请重新输入")
            except KeyboardInterrupt:
                print("\n👋 用户取消输入")
                return None
    
    def make_prediction_and_save(self, current_data):
        """进行预测并保存到所有文件"""
        print(f"\n🎯 基于{current_data['year']}年{current_data['period']}期数据进行预测")
        print("=" * 50)

        current_numbers = current_data['numbers']
        print(f"当期开奖: {current_numbers}")

        # 1. 自动验证上期预测（如果存在）
        self.verify_previous_prediction(current_data['year'], current_data['period'], current_numbers)

        # 2. 添加真实数据到主文件
        self.add_real_data_to_main_file(current_data['year'], current_data['period'], current_numbers)

        # 3. 执行预测
        predicted_numbers, confidence = self.predict_next_period(current_numbers)

        print(f"\n🔮 预测下期:")
        print(f"预测数字: {predicted_numbers}")
        print(f"预测置信度: {confidence:.3f}")

        # 4. 保存预测到CSV文件
        prediction_data = {
            'current_year': current_data['year'],
            'current_period': current_data['period'],
            'predicted_period': f"{current_data['year']}年{current_data['period']+1}期",
            'current_numbers': current_numbers,
            'predicted_numbers': predicted_numbers,
            'confidence': confidence,
            'method': '34.3%增强马尔可夫',
            'notes': '用户输入'
        }

        self.add_prediction_to_csv(prediction_data)

        return prediction_data
    
    def show_recent_predictions(self):
        """显示最近预测"""
        try:
            if not os.path.exists(self.prediction_data_file):
                print("📊 暂无预测数据")
                return
            
            df = pd.read_csv(self.prediction_data_file, encoding='utf-8-sig')
            
            if len(df) == 0:
                print("📊 暂无预测数据")
                return
            
            print(f"\n📊 预测统计")
            print("=" * 50)
            
            total_predictions = len(df)
            verified_predictions = df[df['是否命中'].notna() & (df['是否命中'] != '')].copy()
            hits = len(verified_predictions[verified_predictions['是否命中'] == '是'])
            
            print(f"总预测期数: {total_predictions}")
            print(f"已验证期数: {len(verified_predictions)}")
            print(f"待验证期数: {total_predictions - len(verified_predictions)}")
            
            if len(verified_predictions) > 0:
                hit_rate = hits / len(verified_predictions)
                print(f"命中期数: {hits}")
                print(f"命中率: {hit_rate:.3f} ({hit_rate:.1%})")
            
            # 最近5期预测
            recent = df.tail(5)
            print(f"\n最近{len(recent)}期预测:")
            for _, row in recent.iterrows():
                if row['是否命中'] == '是':
                    status = "✅"
                elif row['是否命中'] == '否':
                    status = "❌"
                else:
                    status = "⏳"
                
                print(f"  {row['预测期号']}: 预测[{row['预测数字1']},{row['预测数字2']}] {status}")
            
        except Exception as e:
            print(f"❌ 显示预测失败: {e}")
    
    def main_menu(self):
        """主菜单"""
        while True:
            print(f"\n🎯 升级版手动输入预测系统")
            print("=" * 40)
            print("1. 输入当期数据并预测下期 (自动保存)")
            print("2. 查看预测统计")
            print("3. 退出系统")
            
            choice = input("\n请选择操作 (1-3): ").strip()
            
            if choice == '1':
                current_data = self.input_current_period_data()
                if current_data:
                    self.make_prediction_and_save(current_data)
            
            elif choice == '2':
                self.show_recent_predictions()
            
            elif choice == '3':
                print("👋 感谢使用，再见！")
                break
            
            else:
                print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    print("🎯 升级版手动输入预测系统")
    print("集成数据管理 - 自动保存到主文件和预测CSV")
    print("=" * 60)
    
    system = UpgradedManualPredictionSystem()
    
    # 初始化系统
    if not system.load_data_and_build_model():
        print("❌ 系统初始化失败")
        return
    
    print("✅ 系统初始化完成")
    print("\n💡 功能说明:")
    print("1. 输入真实开奖数据自动添加到主数据文件")
    print("2. 预测数据自动保存到prediction_data.csv")
    print("3. 完整的数据管理和统计功能")
    
    # 启动主菜单
    system.main_menu()

if __name__ == "__main__":
    main()
